declare module 'imapflow' {
  export class ImapFlow {
    constructor(options: any);
    connect(): Promise<void>;
    logout(): Promise<void>;
    getMailboxLock(mailbox: string): Promise<any>;
    fetch(range: string, options: any, fetchOptions?: any): AsyncIterable<any>;
    messageFlagsAdd(messageId: string, flags: string[]): Promise<void>;
    expunge(): Promise<void>;
    list(): Promise<any[]>;
  }
}

declare module 'mailparser' {
  export function simpleParser(source: any): Promise<any>;
}
