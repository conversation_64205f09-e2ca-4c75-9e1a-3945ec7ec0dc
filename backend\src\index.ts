// 首先加载环境变量（必须在任何其他导入之前）
import dotenv from 'dotenv';
import path from 'path';

dotenv.config({ path: path.join(__dirname, '../.env') });

// 调试：打印环境变量加载状态
console.log('Environment variables loaded:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '[REDACTED]' : 'not set');
console.log('NODE_ENV:', process.env.NODE_ENV);

// 现在导入其他模块（此时环境变量已经加载）
import app, { initializeApp } from './app';
import { logger } from './utils/logger';
import { closePool } from './config/database';
import fs from 'fs';

// 创建必要的目录
const createDirectories = () => {
  const dirs = ['logs', 'uploads'];
  dirs.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      logger.info(`Created directory: ${dirPath}`);
    }
  });
};

// 启动服务器
const startServer = async () => {
  const PORT = process.env.PORT || 3001;
  const NODE_ENV = process.env.NODE_ENV || 'development';

  try {
    // 创建必要目录
    createDirectories();

    // 初始化应用（数据库等）
    await initializeApp();

    const server = app.listen(PORT, () => {
      logger.info(`🚀 邮箱系统后端服务启动成功`, {
        port: PORT,
        environment: NODE_ENV,
        timestamp: new Date().toISOString(),
      });

      logger.info(`📧 API文档地址: http://localhost:${PORT}/health`);
      logger.info(`🌐 前端地址: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
    });

    // 优雅关闭
    const gracefulShutdown = (signal: string) => {
      logger.info(`收到 ${signal} 信号，开始优雅关闭服务器...`);

      server.close(async () => {
        logger.info('HTTP服务器已关闭');

        // 关闭数据库连接池
        try {
          await closePool();
        } catch (error) {
          logger.error('关闭数据库连接池失败:', error);
        }

        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        logger.error('强制关闭服务器');
        process.exit(1);
      }, 10000);
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 未捕获的异常处理
    process.on('uncaughtException', (error) => {
      logger.error('未捕获的异常:', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('未处理的Promise拒绝:', { reason, promise });
      process.exit(1);
    });
  } catch (error) {
    logger.error('启动服务器失败:', error);
    process.exit(1);
  }
};

// 启动应用
startServer();
