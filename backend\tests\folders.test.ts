import request from 'supertest';
import app from '../src/app';
import { TestHelper } from './helpers/testUtils';

describe('Folder API', () => {
  let testUser: any;
  let authToken: string;

  beforeEach(async () => {
    await TestHelper.cleanupTestData();
    const { user, token } = await TestHelper.createTestUser();
    testUser = user;
    authToken = token;
  });

  describe('GET /api/folders', () => {
    it('should get folders list with authentication', async () => {
      const response = await request(app)
        .get('/api/folders')
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateApiResponse(response, 200);
      expect(Array.isArray(response.body.data)).toBe(true);
      
      // 检查是否包含基本文件夹
      const folderNames = response.body.data.map((folder: any) => folder.name);
      expect(folderNames).toContain('收件箱');
      expect(folderNames).toContain('已发送');
      expect(folderNames).toContain('草稿箱');
      expect(folderNames).toContain('垃圾箱');
    });

    it('should include email count and unread count', async () => {
      // 创建一些测试邮件
      await TestHelper.createTestEmail(testUser.id, { folderId: `inbox-${testUser.id}`, isRead: false });
      await TestHelper.createTestEmail(testUser.id, { folderId: `inbox-${testUser.id}`, isRead: true });
      await TestHelper.createTestEmail(testUser.id, { folderId: `sent-${testUser.id}`, isRead: true });

      const response = await request(app)
        .get('/api/folders')
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateApiResponse(response, 200);

      const inboxFolder = response.body.data.find((folder: any) => folder.id === `inbox-${testUser.id}`);
      expect(inboxFolder).toBeDefined();
      expect(inboxFolder.emailCount).toBeGreaterThan(0);
      expect(inboxFolder.unreadCount).toBeGreaterThan(0);

      const sentFolder = response.body.data.find((folder: any) => folder.id === `sent-${testUser.id}`);
      expect(sentFolder).toBeDefined();
      expect(sentFolder.emailCount).toBeGreaterThan(0);
      expect(sentFolder.unreadCount).toBe(0);
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/api/folders');

      TestHelper.validateErrorResponse(response, 401);
    });

    it('should only return folders belonging to authenticated user', async () => {
      // 创建另一个用户
      const { user: otherUser, token: otherToken } = await TestHelper.createTestUser();

      // 获取第一个用户的文件夹
      const response1 = await request(app)
        .get('/api/folders')
        .set(TestHelper.getAuthHeader(authToken));

      // 获取第二个用户的文件夹
      const response2 = await request(app)
        .get('/api/folders')
        .set(TestHelper.getAuthHeader(otherToken));

      TestHelper.validateApiResponse(response1, 200);
      TestHelper.validateApiResponse(response2, 200);

      // 验证每个用户只能看到自己的文件夹
      response1.body.data.forEach((folder: any) => {
        expect(folder.userId).toBe(testUser.id);
      });

      response2.body.data.forEach((folder: any) => {
        expect(folder.userId).toBe(otherUser.id);
      });
    });
  });

  describe('GET /api/folders/:id', () => {
    it('should get folder by id', async () => {
      const response = await request(app)
        .get(`/api/folders/inbox-${testUser.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.id).toBe(`inbox-${testUser.id}`);
      expect(response.body.data.name).toBe('收件箱');
      expect(response.body.data.type).toBe('inbox');
      expect(response.body.data).toHaveProperty('emailCount');
      expect(response.body.data).toHaveProperty('unreadCount');
    });

    it('should include accurate email statistics', async () => {
      // 创建测试邮件
      await TestHelper.createTestEmail(testUser.id, { folderId: `inbox-${testUser.id}`, isRead: false });
      await TestHelper.createTestEmail(testUser.id, { folderId: `inbox-${testUser.id}`, isRead: false });
      await TestHelper.createTestEmail(testUser.id, { folderId: `inbox-${testUser.id}`, isRead: true });

      const response = await request(app)
        .get(`/api/folders/inbox-${testUser.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.emailCount).toBe(3);
      expect(response.body.data.unreadCount).toBe(2);
    });

    it('should fail for non-existent folder', async () => {
      const response = await request(app)
        .get('/api/folders/non-existent-folder')
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateErrorResponse(response, 404);
    });

    it('should fail for folder belonging to another user', async () => {
      // 尝试访问其他用户的文件夹
      const { user: otherUser } = await TestHelper.createTestUser();

      const response = await request(app)
        .get(`/api/folders/inbox-${otherUser.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      // 应该返回403错误，因为无权访问其他用户的文件夹
      TestHelper.validateErrorResponse(response, 403);
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/api/folders/inbox');

      TestHelper.validateErrorResponse(response, 401);
    });
  });

  describe('Folder data consistency', () => {
    it('should maintain consistent email counts across operations', async () => {
      // 创建邮件
      const email = await TestHelper.createTestEmail(testUser.id, { folderId: `inbox-${testUser.id}`, isRead: false });

      // 检查初始状态
      let response = await request(app)
        .get(`/api/folders/inbox-${testUser.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      const initialEmailCount = response.body.data.emailCount;
      const initialUnreadCount = response.body.data.unreadCount;

      // 标记邮件为已读
      await request(app)
        .put(`/api/emails/${email.id}`)
        .set(TestHelper.getAuthHeader(authToken))
        .send({ isRead: true });

      // 检查更新后的状态
      response = await request(app)
        .get(`/api/folders/inbox-${testUser.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      expect(response.body.data.emailCount).toBe(initialEmailCount);
      expect(response.body.data.unreadCount).toBe(initialUnreadCount - 1);
    });

    it('should update counts when email is moved between folders', async () => {
      // 创建邮件在inbox
      const email = await TestHelper.createTestEmail(testUser.id, { folderId: `inbox-${testUser.id}` });

      // 获取初始状态
      const inboxBefore = await request(app)
        .get(`/api/folders/inbox-${testUser.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      const trashBefore = await request(app)
        .get(`/api/folders/trash-${testUser.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      // 移动邮件到垃圾箱
      await request(app)
        .put(`/api/emails/${email.id}`)
        .set(TestHelper.getAuthHeader(authToken))
        .send({ folderId: `trash-${testUser.id}` });

      // 检查更新后的状态
      const inboxAfter = await request(app)
        .get(`/api/folders/inbox-${testUser.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      const trashAfter = await request(app)
        .get(`/api/folders/trash-${testUser.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      expect(inboxAfter.body.data.emailCount).toBe(inboxBefore.body.data.emailCount - 1);
      expect(trashAfter.body.data.emailCount).toBe(trashBefore.body.data.emailCount + 1);
    });
  });
});
