import dotenv from 'dotenv';

// 加载测试环境变量
dotenv.config({ path: '.env.test' });

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.JWT_EXPIRES_IN = '1h';
process.env.JWT_REFRESH_EXPIRES_IN = '7d';
process.env.BCRYPT_ROUNDS = '4'; // 降低加密轮数以提高测试速度

// 全局测试超时
jest.setTimeout(10000);

// 测试前后的清理工作
beforeEach(() => {
  // 每个测试前重置模块
  jest.clearAllMocks();
});

afterEach(() => {
  // 每个测试后清理
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
