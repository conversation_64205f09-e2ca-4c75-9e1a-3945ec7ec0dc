{"version": "0.2.0", "configurations": [{"name": "🚀 开发调试 (TypeScript)", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/index.ts", "cwd": "${workspaceFolder}", "runtimeArgs": ["--loader", "ts-node/esm"], "env": {"NODE_ENV": "development", "TS_NODE_PROJECT": "${workspaceFolder}/tsconfig.json"}, "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "restart": true, "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"name": "🔧 开发调试 (ts-node)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/ts-node/dist/bin.js", "args": ["${workspaceFolder}/src/index.ts"], "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "restart": true, "sourceMaps": true}, {"name": "📦 生产调试 (编译后)", "type": "node", "request": "launch", "program": "${workspaceFolder}/dist/index.js", "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "production"}, "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "preLaunchTask": "npm: build", "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"name": "🔗 附加到 nodemon", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}", "skipFiles": ["<node_internals>/**", "node_modules/**"], "sourceMaps": true}, {"name": "🧪 调试当前测试文件", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["${relativeFile}", "--runInBand", "--no-cache", "--no-coverage"], "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "test"}, "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true}, {"name": "🧪 调试所有测试", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache", "--no-coverage"], "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "test"}, "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true}]}