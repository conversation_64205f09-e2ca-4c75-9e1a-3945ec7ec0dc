{"version": "0.2.0", "configurations": [{"name": "🎯 断点调试 (最佳)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/ts-node/dist/bin.js", "args": ["--files", "--transpile-only=false", "${workspaceFolder}/src/index.ts"], "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "TS_NODE_FILES": "true", "TS_NODE_TRANSPILE_ONLY": "false", "TS_NODE_PROJECT": "${workspaceFolder}/tsconfig.json"}, "envFile": "${workspaceFolder}/.env", "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "restart": true, "sourceMaps": true, "smartStep": true, "stopOnEntry": false, "resolveSourceMapLocations": ["${workspaceFolder}/src/**"], "preLaunchTask": null}, {"name": "🚀 开发调试 (推荐)", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/index.ts", "cwd": "${workspaceFolder}", "runtimeArgs": ["-r", "dotenv/config", "-r", "ts-node/register"], "env": {"NODE_ENV": "development", "DOTENV_CONFIG_PATH": "${workspaceFolder}/.env", "TS_NODE_FILES": "true", "TS_NODE_TRANSPILE_ONLY": "false"}, "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "restart": true, "sourceMaps": true, "smartStep": true, "stopOnEntry": false, "resolveSourceMapLocations": ["${workspaceFolder}/src/**", "!**/node_modules/**"], "outFiles": ["${workspaceFolder}/dist/**/*.js"]}, {"name": "🚀 开发调试 (TypeScript)", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/index.ts", "cwd": "${workspaceFolder}", "runtimeArgs": ["-r", "ts-node/register"], "env": {"NODE_ENV": "development", "TS_NODE_PROJECT": "${workspaceFolder}/tsconfig.json"}, "envFile": "${workspaceFolder}/.env", "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "restart": true, "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"name": "🔧 开发调试 (ts-node)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/ts-node/dist/bin.js", "args": ["${workspaceFolder}/src/index.ts"], "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development"}, "envFile": "${workspaceFolder}/.env", "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "restart": true, "sourceMaps": true}, {"name": "📦 生产调试 (编译后)", "type": "node", "request": "launch", "program": "${workspaceFolder}/dist/index.js", "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "production"}, "envFile": "${workspaceFolder}/.env", "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "preLaunchTask": "npm: build", "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"name": "🔗 附加到 nodemon", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}", "skipFiles": ["<node_internals>/**", "node_modules/**"], "sourceMaps": true}, {"name": "🧪 测试环境变量", "type": "node", "request": "launch", "program": "${workspaceFolder}/scripts/test-env.js", "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "🎯 测试断点功能", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/test-breakpoint.ts", "cwd": "${workspaceFolder}", "runtimeArgs": ["-r", "ts-node/register"], "env": {"NODE_ENV": "development", "TS_NODE_FILES": "true", "TS_NODE_TRANSPILE_ONLY": "false"}, "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true, "smartStep": true, "stopOnEntry": false}, {"name": "🧪 调试当前测试文件", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["${relativeFile}", "--runInBand", "--no-cache", "--no-coverage"], "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "test"}, "envFile": "${workspaceFolder}/.env", "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true}, {"name": "🧪 调试所有测试", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache", "--no-coverage"], "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "test"}, "envFile": "${workspaceFolder}/.env", "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true}]}