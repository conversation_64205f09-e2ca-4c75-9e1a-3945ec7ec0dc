import { Router } from 'express';
import {
  getEmails,
  getEmailById,
  sendEmail,
  updateEmail,
  deleteEmail,
  searchEmails,
} from '../controllers/emailController';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// 所有邮件路由都需要认证
router.use(authenticateToken);

// 邮件CRUD操作
router.get('/', getEmails);
router.get('/:id', getEmailById);
router.post('/', sendEmail);
router.put('/:id', updateEmail);
router.delete('/:id', deleteEmail);

// 邮件搜索
router.post('/search', searchEmails);

export default router;
