# 数据库设置指南

本项目已从内存数据库 (MemoryDatabase) 迁移到 MySQL 数据库。以下是设置和使用指南。

## 前置要求

1. **MySQL 服务器** (版本 5.7 或更高)
2. **Node.js** (版本 16 或更高)
3. **npm** 或 **yarn**

## 安装步骤

### 1. 安装 MySQL

#### Windows:
- 下载并安装 [MySQL Community Server](https://dev.mysql.com/downloads/mysql/)
- 或使用 [XAMPP](https://www.apachefriends.org/index.html)

#### macOS:
```bash
brew install mysql
brew services start mysql
```

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
```

### 2. 创建数据库

登录 MySQL 并创建数据库：

```sql
mysql -u root -p
CREATE DATABASE mailserver CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;
```

### 3. 配置环境变量

复制环境变量示例文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接信息：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your-mysql-password
DB_NAME=mailserver
DB_CONNECTION_LIMIT=10
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000
```

### 4. 初始化数据库表和数据

#### 方法一：使用 SQL 脚本（推荐）

```bash
mysql -u root -p mailserver < scripts/init-database.sql
```

#### 方法二：自动初始化

应用程序启动时会自动创建表结构和初始数据：

```bash
npm run dev
```

## 数据库结构

### 表结构

1. **users** - 用户表
   - 存储用户账户信息
   - 包含邮箱、用户名、密码哈希等

2. **folders** - 文件夹表
   - 存储邮件文件夹信息
   - 支持收件箱、已发送、草稿箱、垃圾箱等类型

3. **emails** - 邮件表
   - 存储邮件内容和元数据
   - 支持 JSON 格式的收件人列表

4. **attachments** - 附件表
   - 存储邮件附件信息
   - 与邮件表关联

5. **contacts** - 联系人表
   - 存储用户联系人信息

### 索引优化

数据库包含以下索引以提高查询性能：

- 用户表：邮箱、用户名索引
- 邮件表：用户ID、文件夹ID、发送者邮箱、读取状态等索引
- 其他表：相关外键和查询字段索引

## 开发和测试

### 启动开发服务器

```bash
npm run dev
```

### 运行测试

```bash
npm test
```

### 构建生产版本

```bash
npm run build
npm start
```

## 数据迁移

如果您之前使用的是内存数据库版本，所有数据将在重启后丢失。新的 MySQL 版本会：

1. 自动创建必要的表结构
2. 插入示例数据（如果数据库为空）
3. 保持数据持久化

### 默认测试账户

- **邮箱**: <EMAIL>
- **用户名**: testuser
- **密码**: 123456

## 故障排除

### 常见问题

1. **连接失败**
   - 检查 MySQL 服务是否运行
   - 验证 `.env` 文件中的数据库配置
   - 确认数据库用户权限

2. **表不存在**
   - 运行数据库初始化脚本
   - 检查数据库名称是否正确

3. **权限错误**
   - 确保 MySQL 用户有足够权限
   - 检查防火墙设置

### 日志查看

应用程序日志位于 `logs/` 目录：

- `combined.log` - 所有日志
- `error.log` - 错误日志

### 数据库连接测试

应用程序启动时会自动测试数据库连接。如果连接失败，请检查：

1. MySQL 服务状态
2. 网络连接
3. 认证信息
4. 数据库是否存在

## 性能优化

### 连接池配置

在 `.env` 文件中调整连接池设置：

```env
DB_CONNECTION_LIMIT=10    # 最大连接数
DB_ACQUIRE_TIMEOUT=60000  # 获取连接超时时间
DB_TIMEOUT=60000          # 查询超时时间
```

### 查询优化

- 使用适当的索引
- 避免 N+1 查询问题
- 使用分页查询大量数据
- 定期分析慢查询日志

## 备份和恢复

### 备份数据库

```bash
mysqldump -u root -p mailserver > backup.sql
```

### 恢复数据库

```bash
mysql -u root -p mailserver < backup.sql
```

## 生产环境部署

1. 使用专用的 MySQL 服务器
2. 配置 SSL 连接
3. 设置适当的用户权限
4. 定期备份数据
5. 监控数据库性能

## 支持

如果遇到问题，请检查：

1. 应用程序日志
2. MySQL 错误日志
3. 网络连接状态
4. 配置文件设置
