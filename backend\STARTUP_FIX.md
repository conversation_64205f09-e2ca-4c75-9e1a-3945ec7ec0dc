# Backend 启动问题修复指南

## 🚨 当前问题

根据错误日志分析，主要问题是：
1. **MySQL 服务未运行** - `ECONNREFUSED` 错误
2. **数据库配置不正确** - `.env` 文件配置过时
3. **数据库不存在** - `mailserver` 数据库未创建

## 🛠️ 快速修复步骤

### 步骤 1: 检查 MySQL 服务状态
```bash
npm run db:check
```

### 步骤 2: 启动 MySQL 服务

#### Windows 用户：
```bash
# 方法1: 使用命令行（以管理员身份运行）
net start mysql

# 方法2: 使用服务管理器
# 按 Win+R，输入 services.msc，找到 MySQL 服务并启动

# 方法3: 如果使用 XAMPP
# 打开 XAMPP 控制面板，点击 MySQL 的 "Start" 按钮
```

#### macOS 用户：
```bash
brew services start mysql
```

#### Linux 用户：
```bash
sudo systemctl start mysql
```

### 步骤 3: 验证数据库连接
```bash
npm run db:test:simple
```

### 步骤 4: 创建数据库（如果不存在）
```bash
# 方法1: 使用脚本
npm run db:init

# 方法2: 手动创建
mysql -u root -p -e "CREATE DATABASE mailserver CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

### 步骤 5: 启动后端服务
```bash
npm run dev
```

## 🔧 配置文件已修复

已自动修复以下配置：

### `.env` 文件更新
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=          # 请设置您的 MySQL 密码
DB_NAME=mailserver
```

### 数据库默认名称修复
- 从 `mailserver` 更正为 `mailserver`

## 📋 验证清单

- [ ] MySQL 服务正在运行
- [ ] 数据库连接测试通过
- [ ] `mailserver` 数据库已创建
- [ ] 数据库表已初始化
- [ ] 后端服务启动成功

## 🆘 常见问题解决

### 问题 1: MySQL 服务启动失败
**解决方案：**
1. 检查是否已安装 MySQL
2. 检查端口 3306 是否被占用
3. 查看 MySQL 错误日志

### 问题 2: 数据库连接被拒绝
**解决方案：**
1. 确认 MySQL 用户名和密码
2. 检查 `.env` 文件中的 `DB_PASSWORD`
3. 验证用户权限：
   ```sql
   GRANT ALL PRIVILEGES ON mailserver.* TO 'root'@'localhost';
   FLUSH PRIVILEGES;
   ```

### 问题 3: 数据库不存在
**解决方案：**
```bash
mysql -u root -p -e "CREATE DATABASE mailserver CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

### 问题 4: 表不存在
**解决方案：**
```bash
npm run db:init
```

## 🔍 调试命令

```bash
# 检查 MySQL 服务状态
npm run db:check

# 简单连接测试
npm run db:test:simple

# 完整数据库测试
npm run db:test

# 查看详细日志
npm run dev
```

## 📞 获取帮助

如果问题仍然存在：
1. 查看终端错误日志
2. 检查 `logs/error.log` 文件
3. 运行 `npm run db:check` 获取详细诊断信息

---

*修复完成后，后端服务应该能够正常启动并连接到 MySQL 数据库。*
