#!/usr/bin/env node

/**
 * MySQL 服务检查脚本
 * 检查 MySQL 服务是否运行，如果没有运行则提供启动指导
 */

const { exec } = require('child_process');
const os = require('os');

// 颜色输出
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
};

function checkMySQLService() {
  console.log(colors.cyan('🔍 检查 MySQL 服务状态...\n'));

  const platform = os.platform();
  let command;

  switch (platform) {
    case 'win32':
      command = 'sc query mysql';
      break;
    case 'darwin':
      command = 'brew services list | grep mysql';
      break;
    case 'linux':
      command = 'systemctl is-active mysql';
      break;
    default:
      console.log(colors.red('❌ 不支持的操作系统'));
      return;
  }

  exec(command, (error, stdout, stderr) => {
    if (platform === 'win32') {
      if (error || stdout.includes('STOPPED') || stdout.includes('服务名无效')) {
        console.log(colors.red('❌ MySQL 服务未运行'));
        console.log(colors.yellow('\n💡 启动 MySQL 服务的方法:'));
        console.log('   方法1: 使用服务管理器');
        console.log('     - 按 Win+R，输入 services.msc');
        console.log('     - 找到 MySQL 服务，右键选择"启动"');
        console.log('\n   方法2: 使用命令行（以管理员身份运行）');
        console.log('     net start mysql');
        console.log('\n   方法3: 如果使用 XAMPP');
        console.log('     - 打开 XAMPP 控制面板');
        console.log('     - 点击 MySQL 的 "Start" 按钮');
      } else if (stdout.includes('RUNNING')) {
        console.log(colors.green('✅ MySQL 服务正在运行'));
        checkConnection();
      } else {
        console.log(colors.yellow('⚠️  无法确定 MySQL 服务状态'));
        console.log('请手动检查 MySQL 是否安装和运行');
      }
    } else if (platform === 'darwin') {
      if (error || !stdout.includes('started')) {
        console.log(colors.red('❌ MySQL 服务未运行'));
        console.log(colors.yellow('\n💡 启动 MySQL 服务:'));
        console.log('   brew services start mysql');
      } else {
        console.log(colors.green('✅ MySQL 服务正在运行'));
        checkConnection();
      }
    } else if (platform === 'linux') {
      if (error || stdout.trim() !== 'active') {
        console.log(colors.red('❌ MySQL 服务未运行'));
        console.log(colors.yellow('\n💡 启动 MySQL 服务:'));
        console.log('   sudo systemctl start mysql');
      } else {
        console.log(colors.green('✅ MySQL 服务正在运行'));
        checkConnection();
      }
    }
  });
}

function checkConnection() {
  console.log(colors.cyan('\n🔗 测试数据库连接...'));
  
  // 尝试连接数据库
  exec('mysql -u root -e "SELECT 1;" 2>/dev/null', (error, stdout, stderr) => {
    if (error) {
      console.log(colors.red('❌ 数据库连接失败'));
      console.log(colors.yellow('\n💡 可能的原因:'));
      console.log('   1. MySQL 密码设置问题');
      console.log('   2. 用户权限不足');
      console.log('   3. 端口配置错误');
      console.log('\n💡 解决方法:');
      console.log('   1. 检查 .env 文件中的数据库配置');
      console.log('   2. 确保 DB_PASSWORD 正确');
      console.log('   3. 运行: npm run db:test:simple');
    } else {
      console.log(colors.green('✅ 数据库连接正常'));
      checkDatabase();
    }
  });
}

function checkDatabase() {
  console.log(colors.cyan('\n📊 检查数据库是否存在...'));
  
  exec('mysql -u root -e "SHOW DATABASES LIKE \'mailserver\';" 2>/dev/null', (error, stdout, stderr) => {
    if (error) {
      console.log(colors.yellow('⚠️  无法检查数据库'));
    } else if (stdout.includes('mailserver')) {
      console.log(colors.green('✅ 数据库 mailserver 存在'));
      console.log(colors.green('\n🎉 所有检查通过！可以启动后端服务了。'));
      console.log(colors.blue('\n▶️  运行: npm run dev'));
    } else {
      console.log(colors.yellow('⚠️  数据库 mailserver 不存在'));
      console.log(colors.yellow('\n💡 创建数据库:'));
      console.log('   1. 运行: npm run db:init');
      console.log('   2. 或手动创建:');
      console.log('      mysql -u root -e "CREATE DATABASE mailserver CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"');
    }
  });
}

// 运行检查
if (require.main === module) {
  checkMySQLService();
}

module.exports = { checkMySQLService };
