// 断点测试文件
// 用于验证调试器和断点是否正常工作

console.log('🔍 开始断点测试...');

// 测试函数 1：简单变量断点
const testSimpleBreakpoint = () => {
  console.log('测试简单断点');
  const message = 'Hello Debug!'; // 👈 在这里设置断点
  const number = 42;
  const array = [1, 2, 3, 4, 5];
  
  console.log('消息:', message);
  console.log('数字:', number);
  console.log('数组:', array);
  
  return { message, number, array };
};

// 测试函数 2：循环断点
const testLoopBreakpoint = () => {
  console.log('测试循环断点');
  const results = [];
  
  for (let i = 0; i < 5; i++) {
    const value = i * 2; // 👈 在这里设置断点
    results.push(value);
    console.log(`循环 ${i}: ${value}`);
  }
  
  return results;
};

// 测试函数 3：异步断点
const testAsyncBreakpoint = async () => {
  console.log('测试异步断点');
  
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
  
  await delay(100);
  const asyncResult = 'Async completed'; // 👈 在这里设置断点
  console.log('异步操作完成:', asyncResult);
  
  return asyncResult;
};

// 测试函数 4：错误处理断点
const testErrorBreakpoint = () => {
  console.log('测试错误处理断点');
  
  try {
    const data = { name: 'Test', value: 100 };
    const result = data.name.toUpperCase(); // 👈 在这里设置断点
    console.log('处理结果:', result);
    return result;
  } catch (error) {
    const errorMessage = `错误: ${error}`; // 👈 在这里也可以设置断点
    console.error(errorMessage);
    return errorMessage;
  }
};

// 主执行函数
const runTests = async () => {
  console.log('\n🚀 开始执行所有测试...\n');
  
  // 测试 1
  console.log('=== 测试 1: 简单断点 ===');
  const result1 = testSimpleBreakpoint();
  console.log('结果 1:', result1);
  
  // 测试 2
  console.log('\n=== 测试 2: 循环断点 ===');
  const result2 = testLoopBreakpoint();
  console.log('结果 2:', result2);
  
  // 测试 3
  console.log('\n=== 测试 3: 异步断点 ===');
  const result3 = await testAsyncBreakpoint();
  console.log('结果 3:', result3);
  
  // 测试 4
  console.log('\n=== 测试 4: 错误处理断点 ===');
  const result4 = testErrorBreakpoint();
  console.log('结果 4:', result4);
  
  console.log('\n✅ 所有测试完成!');
  
  return {
    test1: result1,
    test2: result2,
    test3: result3,
    test4: result4
  };
};

// 执行测试
if (require.main === module) {
  runTests()
    .then(results => {
      console.log('\n📊 最终结果:', JSON.stringify(results, null, 2));
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

export { testSimpleBreakpoint, testLoopBreakpoint, testAsyncBreakpoint, testErrorBreakpoint, runTests };
