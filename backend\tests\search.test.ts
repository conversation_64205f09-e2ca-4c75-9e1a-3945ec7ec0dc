import request from 'supertest';
import app from '../src/app';
import { TestHelper } from './helpers/testUtils';

describe('Email Search API', () => {
  let testUser: any;
  let authToken: string;

  beforeEach(async () => {
    await TestHelper.cleanupTestData();
    const { user, token } = await TestHelper.createTestUser();
    testUser = user;
    authToken = token;

    // 创建一些测试邮件用于搜索
    await TestHelper.createTestEmail(testUser.id, {
      subject: 'JavaScript开发指南',
      contentText: '这是一份关于JavaScript开发的详细指南',
      senderEmail: '<EMAIL>',
      senderName: '开发团队',
    });

    await TestHelper.createTestEmail(testUser.id, {
      subject: '项目会议通知',
      contentText: '明天下午2点在会议室A举行项目讨论会议',
      senderEmail: '<EMAIL>',
      senderName: '项目经理',
    });

    await TestHelper.createTestEmail(testUser.id, {
      subject: 'React组件开发',
      contentText: '关于React组件的最佳实践和开发技巧',
      senderEmail: '<EMAIL>',
      senderName: '前端团队',
    });

    await TestHelper.createTestEmail(testUser.id, {
      subject: '数据库优化建议',
      contentText: '针对当前数据库性能的优化建议和实施方案',
      senderEmail: '<EMAIL>',
      senderName: '数据库管理员',
    });
  });

  describe('POST /api/emails/search', () => {
    it('should search emails by subject', async () => {
      const searchData = {
        query: 'JavaScript',
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateApiResponse(response, 200);
      expect(Array.isArray(response.body.data.data)).toBe(true);
      expect(response.body.data.data.length).toBeGreaterThan(0);

      // 验证搜索结果包含关键词
      const foundEmail = response.body.data.data.find((email: any) =>
        email.subject.includes('JavaScript')
      );
      expect(foundEmail).toBeDefined();
    });

    it('should search emails by content', async () => {
      const searchData = {
        query: '会议室',
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.data.length).toBeGreaterThan(0);

      const foundEmail = response.body.data.data.find((email: any) =>
        email.contentText.includes('会议室')
      );
      expect(foundEmail).toBeDefined();
    });

    it('should search emails by sender', async () => {
      const searchData = {
        query: '<EMAIL>',
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.data.length).toBeGreaterThan(0);

      const foundEmail = response.body.data.data.find((email: any) =>
        email.senderEmail === '<EMAIL>'
      );
      expect(foundEmail).toBeDefined();
    });

    it('should search emails by sender name', async () => {
      const searchData = {
        query: '项目经理',
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.data.length).toBeGreaterThan(0);

      const foundEmail = response.body.data.data.find((email: any) =>
        email.senderName.includes('项目经理')
      );
      expect(foundEmail).toBeDefined();
    });

    it('should return empty results for non-matching query', async () => {
      const searchData = {
        query: '不存在的关键词xyz123',
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.data.length).toBe(0);
    });

    it('should be case insensitive', async () => {
      const searchData = {
        query: 'javascript', // 小写
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.data.length).toBeGreaterThan(0);
    });

    it('should support partial matching', async () => {
      const searchData = {
        query: '开发', // 部分匹配
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.data.length).toBeGreaterThan(0);

      // 应该找到包含"开发"的邮件
      const hasMatchingEmail = response.body.data.data.some((email: any) =>
        email.subject.includes('开发') ||
        email.contentText.includes('开发') ||
        email.senderName.includes('开发')
      );
      expect(hasMatchingEmail).toBe(true);
    });

    it('should only return emails belonging to authenticated user', async () => {
      // 创建另一个用户和邮件
      const { user: otherUser } = await TestHelper.createTestUser();
      await TestHelper.createTestEmail(otherUser.id, {
        subject: 'JavaScript高级教程',
        contentText: '这是另一个用户的JavaScript邮件',
      });

      const searchData = {
        query: 'JavaScript',
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateApiResponse(response, 200);
      
      // 验证所有返回的邮件都属于当前用户
      response.body.data.data.forEach((email: any) => {
        expect(email.userId).toBe(testUser.id);
      });
    });

    it('should fail without authentication', async () => {
      const searchData = {
        query: 'JavaScript',
      };

      const response = await request(app)
        .post('/api/emails/search')
        .send(searchData);

      TestHelper.validateErrorResponse(response, 401);
    });

    it('should fail with empty query', async () => {
      const searchData = {
        query: '',
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateErrorResponse(response, 400);
    });

    it('should fail without query parameter', async () => {
      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send({});

      TestHelper.validateErrorResponse(response, 400);
    });

    it('should handle special characters in query', async () => {
      const searchData = {
        query: '@example.com',
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.data.length).toBeGreaterThan(0);
    });

    it('should support multiple word search', async () => {
      const searchData = {
        query: 'React组件', // 搜索不带空格的组合词
      };

      const response = await request(app)
        .post('/api/emails/search')
        .set(TestHelper.getAuthHeader(authToken))
        .send(searchData);

      TestHelper.validateApiResponse(response, 200);

      // 应该找到包含"React组件"的邮件
      const foundEmail = response.body.data.data.find((email: any) =>
        email.subject.includes('React组件')
      );
      expect(foundEmail).toBeDefined();
    });
  });
});
