{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 01:54:37"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 01:54:37"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 01:54:37"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 01:58:21"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 01:58:22"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 01:58:22"}
{"DB_PASSWORD":"not set","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 01:59:12"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 01:59:12"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 01:59:13"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 01:59:13"}
{"DB_PASSWORD":"not set","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 01:59:12"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 01:59:12"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 01:59:13"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 01:59:13"}
{"DB_PASSWORD":"not set","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 01:59:12"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 01:59:12"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 01:59:13"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 01:59:13"}
{"DB_PASSWORD":"not set","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 01:59:20"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 01:59:20"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 01:59:20"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 01:59:20"}
{"DB_PASSWORD":"not set","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:00:20"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:00:20"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:00:20"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:00:20"}
{"DB_PASSWORD":"not set","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:00:46"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:00:46"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:00:46"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:00:46"}
{"DB_PASSWORD":"not set","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:00:46"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:00:46"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:00:46"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:00:46"}
{"DB_PASSWORD":"not set","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:00:55"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:00:55"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:00:55"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:00:55"}
{"DB_PASSWORD":"not set","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:01:05"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:01:05"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:01:05"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:01:05"}
{"DB_PASSWORD":"not set","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:01:19"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:01:19"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:01:19"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:01:19"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:01:54"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:01:55"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:01:55"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:01:55"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:01:55"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:01:55"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:01:55"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:01:55"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:01:55"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:01:55"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:01:55"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:02:03"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:02:03"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:02:03"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:02:03"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:02:03"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:02:03"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:02:03"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:02:03"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:02:04"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:02:04"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:02:04"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:02:04"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:02:04"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:02:04"}
{"body":{"email":"<EMAIL>","password":"123456"},"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"trbdc0t4toj3y7p0idi0xt","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-15 02:03:31","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"304ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":401,"timestamp":"2025-06-15 02:03:31","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"email":"<EMAIL>","password":"123456"},"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"wqkckmky9xqx0tdqzr6q5","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-15 02:03:38","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"258ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":401,"timestamp":"2025-06-15 02:03:38","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"level":"error","message":"Failed to create user: Field 'domain_id' doesn't have a default value","service":"email-system-backend","sql":"\n      INSERT INTO users (id, email, username, display_name, avatar_url, password_hash, is_active, created_at, updated_at)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ","sqlMessage":"Field 'domain_id' doesn't have a default value","sqlState":"HY000","stack":"Error: Field 'domain_id' doesn't have a default value\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at MySQLDatabase.createUser (C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\database\\mysqlDatabase.ts:22:23)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:46:33","timestamp":"2025-06-15 02:03:56"}
{"body":{"displayName":"iam","email":"<EMAIL>","password":"123@qwe","username":"iam"},"ip":"::1","level":"error","message":"Error occurred: Field 'domain_id' doesn't have a default value","method":"POST","params":{},"query":{},"requestId":"cwdqvbq9nyealho8c4dnwk","service":"email-system-backend","stack":"Error: Field 'domain_id' doesn't have a default value\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at MySQLDatabase.createUser (C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\database\\mysqlDatabase.ts:22:23)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:46:33","timestamp":"2025-06-15 02:03:56","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"374ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:03:56","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:09:29"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:09:29"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:09:29"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:09:29"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:09:30"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:09:30"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:09:30"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:09:30"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:09:30"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:09:30"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:09:30"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:09:30"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:09:30"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:09:30"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:09:51"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:10:00"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:10:00"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:10:00"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:10:00"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:10:00"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:10:00"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:10:00"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:10:00"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:10:01"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:10:01"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:10:01"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:10:01"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:10:01"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:10:01"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:10:29"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:10:29"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:10:29"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:10:29"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:10:29"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:10:29"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:10:29"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:10:29"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:10:30"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:10:30"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:10:30"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:10:30"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:10:30"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:10:30"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:10:39"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:10:39"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:10:39"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:10:39"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:10:39"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:10:39"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:10:39"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:10:39"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:10:39"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:10:40"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:10:40"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:10:40"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:10:40"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:10:40"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:11:06"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:11:06"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:11:06"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:11:06"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:06"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:11:06"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:11:07"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:11:07"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:07"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:11:07"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:07"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:11:07"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:11:07"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:11:07"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:11:34"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:11:42"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:11:42"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:11:43"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:11:54"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:11:54"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:11:54"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:11:54"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:54"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:11:54"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:11:54"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:11:54"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:55"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:11:55"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:11:55"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:11:55"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:11:55"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:11:55"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:12:05"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:12:05"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:12:05"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:12:05"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:12:05"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:12:05"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:12:05"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:12:05"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:12:06"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:12:06"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:12:06"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:12:06"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:12:06"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:12:06"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:12:19"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:12:30"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:12:30"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:12:30"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:12:30"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:12:31"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:12:31"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:12:31"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:12:31"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:12:31"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:12:31"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:12:31"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:12:31"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:12:31"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:12:31"}
{"duration":"74ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"email-system-backend","status":200,"timestamp":"2025-06-15 02:12:49","url":"/","userAgent":"curl/8.6.0"}
{"duration":"56ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"email-system-backend","status":200,"timestamp":"2025-06-15 02:13:00","url":"/check?email=<EMAIL>","userAgent":"curl/8.6.0"}
{"domainId":"domain-1","email":"<EMAIL>","level":"info","message":"User created successfully","service":"email-system-backend","timestamp":"2025-06-15 02:13:09","userId":"c6c175af-502f-48cf-b48f-1aaa82f4a237"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"email-system-backend","timestamp":"2025-06-15 02:13:09","userId":"c6c175af-502f-48cf-b48f-1aaa82f4a237"}
{"duration":"509ms","ip":"::1","level":"info","message":"HTTP Request","method":"POST","service":"email-system-backend","status":201,"timestamp":"2025-06-15 02:13:09","url":"/register","userAgent":"curl/8.6.0"}
{"body":{"displayName":"�����û�2","email":"<EMAIL>","password":"123456","username":"testuser2"},"ip":"::1","level":"error","message":"Error occurred: 域名 nonexistent.com 不存在或未激活","method":"POST","params":{},"query":{},"requestId":"95xmnyd1m9fo5ttj2nmblg","service":"email-system-backend","stack":"Error: 域名 nonexistent.com 不存在或未激活\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:49:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-15 02:13:18","url":"/api/auth/register","userAgent":"curl/8.6.0"}
{"duration":"92ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":400,"timestamp":"2025-06-15 02:13:18","url":"/api/auth/register","userAgent":"curl/8.6.0"}
{"body":{"displayName":"iam","email":"<EMAIL>","password":"123@qwe","username":"iam"},"ip":"::1","level":"error","message":"Error occurred: 域名 blinededby.love 不存在或未激活","method":"POST","params":{},"query":{},"requestId":"sbf0c6p946plup34rkgnj","service":"email-system-backend","stack":"Error: 域名 blinededby.love 不存在或未激活\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:49:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-15 02:15:24","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"87ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":400,"timestamp":"2025-06-15 02:15:24","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"email":"<EMAIL>","password":"123456"},"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"h17hz72ijwae2a65hk9kmk","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:151:11","timestamp":"2025-06-15 02:17:25","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"267ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":401,"timestamp":"2025-06-15 02:17:25","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"displayName":"iam","email":"<EMAIL>","password":"123@qwe","username":"iam"},"ip":"::1","level":"error","message":"Error occurred: 域名 blinededby.love 不存在或未激活","method":"POST","params":{},"query":{},"requestId":"eaosfg53k3mdunp6hxbwj","service":"email-system-backend","stack":"Error: 域名 blinededby.love 不存在或未激活\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:49:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-15 02:17:53","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"83ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":400,"timestamp":"2025-06-15 02:17:53","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"domainId":"domain-1","email":"<EMAIL>","level":"info","message":"User created successfully","service":"email-system-backend","timestamp":"2025-06-15 02:18:53","userId":"a7862a20-4454-4518-ac55-d1c230f21f0f"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"email-system-backend","timestamp":"2025-06-15 02:18:53","userId":"a7862a20-4454-4518-ac55-d1c230f21f0f"}
{"duration":"383ms","ip":"::1","level":"info","message":"HTTP Request","method":"POST","service":"email-system-backend","status":201,"timestamp":"2025-06-15 02:18:53","url":"/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"113ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"email-system-backend","status":304,"timestamp":"2025-06-15 02:18:54","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","params":{},"query":{},"requestId":"ahu55mb20fqypomyoc9q","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:186:3)","timestamp":"2025-06-15 02:19:04","url":"/api/auth/logout","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":401,"timestamp":"2025-06-15 02:19:04","url":"/api/auth/logout","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"email":"<EMAIL>","password":"123@qwe"},"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"y2jr34goc6scn4mhmxsq6w","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:140:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-15 02:19:11","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"30ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":401,"timestamp":"2025-06-15 02:19:11","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"email":"<EMAIL>","password":"123456"},"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"h9307vgla0ttxl0q801d","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:151:11","timestamp":"2025-06-15 02:19:54","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"263ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":401,"timestamp":"2025-06-15 02:19:54","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","service":"email-system-backend","timestamp":"2025-06-15 02:20:26","userId":"a7862a20-4454-4518-ac55-d1c230f21f0f"}
{"duration":"256ms","ip":"::1","level":"info","message":"HTTP Request","method":"POST","service":"email-system-backend","status":200,"timestamp":"2025-06-15 02:20:26","url":"/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"61ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"email-system-backend","status":304,"timestamp":"2025-06-15 02:20:26","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","params":{},"query":{},"requestId":"wlqxk3tpzmdscx06k5625","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:186:3)","timestamp":"2025-06-15 02:20:28","url":"/api/auth/logout","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":401,"timestamp":"2025-06-15 02:20:28","url":"/api/auth/logout","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"domainId":"domain-1","email":"<EMAIL>","level":"info","message":"User created successfully","service":"email-system-backend","timestamp":"2025-06-15 02:21:16","userId":"8cff0683-32be-4455-bb85-95b20ae23bc3"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"email-system-backend","timestamp":"2025-06-15 02:21:16","userId":"8cff0683-32be-4455-bb85-95b20ae23bc3"}
{"duration":"386ms","ip":"::1","level":"info","message":"HTTP Request","method":"POST","service":"email-system-backend","status":201,"timestamp":"2025-06-15 02:21:16","url":"/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"59ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"email-system-backend","status":304,"timestamp":"2025-06-15 02:21:17","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"1mludmb2e78ybccnqcqcf","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:21:37","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"33ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:21:37","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"oceaiogli9jp89gtolvs","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:21:38","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"32ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:21:38","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"5hsdpu10liendx42gq5vg","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:21:40","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"40ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:21:40","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"z3wfrvuhmmh0ons49dna8x","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:21:44","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"32ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:21:44","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"de4x2v5wl3hx74c32e8wv","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:22:13","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"30ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:22:13","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"au01vho7x6bsu5l4t41je","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:22:14","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"29ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:22:14","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"pto0upycgvlqfucszl5rkj","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:22:16","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"30ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:22:16","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭服务器...","service":"email-system-backend","timestamp":"2025-06-15 02:22:16"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:22:53"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:22:53"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:22:54"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"2kv8u9kvfj5gf5l75wid","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:05","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"59ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:23:05","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"4y69p2uofsdx5itjwyjg1o","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:06","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:23:06","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"myjzhecdp8bmscknyz008","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:09","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:23:09","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"sr0k5qzuq6fwc616y9f4td","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:13","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"39ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:23:13","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"029uct99ggjylaj3qz9tbsr","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:51","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"29ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:23:51","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"211lw37c8sqr87no9h3x4","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:52","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:23:52","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"lpc5kj4brbcm1ilgm4q2z","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:54","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:23:54","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"r5z5hpogs7dogf4x8t67rg","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:03","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"29ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:27:03","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"r8wg59w8sm812bbw1ar3bl","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:04","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:27:04","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"90tkwmd2tlka8bd8m5qj","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:05","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:27:05","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"wiamhvtzx0ha3rhymvkcuc","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:07","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:27:07","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"bt05fhzg9kgz1bihpwarl","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:11","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:27:11","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"de3moxn9mnfkacqtndvdd","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:27","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"30ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:27:27","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"5kzglp9oiaqx7ek32zc73","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:28","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:27:28","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"155fi5ipd3zhbaqq35pr1uk","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:30","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"33ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:27:30","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"4w393bdnn5y8z21hc11wgf","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:34","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"29ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:27:34","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:27:45"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:27:45"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:27:46"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:28:49"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:28:49"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:28:49"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:28:49"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:28:50"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:28:50"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:28:50"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:28:50"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:28:50"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:28:50"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:28:50"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:28:50"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:28:50"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:28:50"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"62mthyfjitrww6xjwk4z3","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:29:17","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"66ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:29:17","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"ttptwfywjg9hyp1y1hpd0a","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:29:18","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:29:18","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"oa6wmwyhm9qv9n1zo9u58r","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:29:20","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"26ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:29:20","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"iyjy3kuxjfrmutnxuz0zkf","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:29:24","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:29:24","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:29:49"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:29:49"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:29:50"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:30:48"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:30:48"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:30:48"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:30:48"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:30:49"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:30:49"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:30:49"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:30:49"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:30:49"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:30:49"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:30:49"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:30:49"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:30:49"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:30:49"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"xaob5acu0xie24yixb37a","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:31:14","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"71ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:31:14","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"8gbbaehd6nar6s4t8su2ds","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:31:15","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"37ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:31:15","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"t6zfajkvf2i5u3xyc347oa","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:31:18","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"32ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:31:18","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"mrgwwcb3shut3v2u14cfk","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:31:22","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"33ms","ip":"::1","level":"warn","message":"HTTP Request","method":"POST","service":"email-system-backend","status":500,"timestamp":"2025-06-15 02:31:22","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭服务器...","service":"email-system-backend","timestamp":"2025-06-15 02:31:59"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:32:18"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:32:18"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:32:19"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭服务器...","service":"email-system-backend","timestamp":"2025-06-15 02:32:30"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:32:57"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:32:57"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:32:57"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:32:57"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:32:57"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:32:57"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:32:57"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:32:57"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:32:58"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:32:58"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:32:58"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:32:58"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:32:58"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:32:58"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:36:10"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:36:10"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:36:11"}
{"DB_ACQUIRE_TIMEOUT":"30000","DB_CONNECTION_LIMIT":"5","DB_HOST":"blindedby.love","DB_NAME":"mailserver","DB_PASSWORD":"[REDACTED]","DB_PORT":"3306","DB_TIMEOUT":"30000","DB_USER":"mailadmin","level":"info","message":"Database environment variables:","service":"email-system-backend","timestamp":"2025-06-15 02:42:53"}
{"database":"mailserver","host":"blindedby.love","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:42:53"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:42:53"}
{"level":"info","message":"Getting connection from pool...","service":"email-system-backend","timestamp":"2025-06-15 02:42:53"}
{"level":"info","message":"Connection acquired successfully","service":"email-system-backend","timestamp":"2025-06-15 02:42:54"}
{"level":"info","message":"Database ping successful","service":"email-system-backend","timestamp":"2025-06-15 02:42:54"}
{"level":"info","message":"Connected to MySQL server version: 8.0.42-0ubuntu0.24.04.1","service":"email-system-backend","timestamp":"2025-06-15 02:42:54"}
{"level":"info","message":"Connection released back to pool","service":"email-system-backend","timestamp":"2025-06-15 02:42:54"}
{"level":"info","message":"Database tables initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:42:54"}
{"level":"info","message":"Database already has data, skipping seed","service":"email-system-backend","timestamp":"2025-06-15 02:42:54"}
{"level":"info","message":"Application initialized successfully","service":"email-system-backend","timestamp":"2025-06-15 02:42:54"}
{"environment":"development","level":"info","message":"🚀 邮箱系统后端服务启动成功","port":"3001","service":"email-system-backend","timestamp":"2025-06-15 02:42:54"}
{"level":"info","message":"📧 API文档地址: http://localhost:3001/health","service":"email-system-backend","timestamp":"2025-06-15 02:42:54"}
{"level":"info","message":"🌐 前端地址: http://localhost:5173","service":"email-system-backend","timestamp":"2025-06-15 02:42:54"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭服务器...","service":"email-system-backend","timestamp":"2025-06-15 02:45:35"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:50:07"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:50:07"}
{"database":"mailserver","host":"localhost","level":"info","message":"MySQL connection pool created","port":3306,"service":"email-system-backend","timestamp":"2025-06-15 02:51:13"}
{"level":"info","message":"Initializing application...","service":"email-system-backend","timestamp":"2025-06-15 02:51:13"}
