# 环境变量调试指南

## 🚨 问题描述

在调试模式下，应用可能无法正确读取 `.env` 文件中的环境变量，导致使用默认配置（如数据库连接到 localhost:3306）。

## 🔍 诊断步骤

### 1. 快速检查环境变量

运行环境变量测试脚本：

```bash
npm run env:test
```

这会显示：
- .env 文件是否存在
- 环境变量是否正确加载
- 各个配置项的值

### 2. 使用调试配置测试

在 VS Code 中：
1. 按 `Ctrl+Shift+D` 打开调试面板
2. 选择 `🧪 测试环境变量` 配置
3. 点击运行，查看输出

## ✅ 解决方案

### 方案一：使用推荐的调试配置

选择 `🚀 开发调试 (推荐)` 配置，它会：
- 自动加载 .env 文件
- 使用 dotenv/config 预加载环境变量
- 确保在应用启动前环境变量已就绪

### 方案二：检查 .env 文件位置

确保 `.env` 文件位于正确位置：
```
backend/
├── .env          ← 应该在这里
├── src/
├── package.json
└── ...
```

### 方案三：手动指定环境变量

在调试配置中添加环境变量：

```json
{
  "name": "手动环境变量调试",
  "type": "node",
  "request": "launch",
  "program": "${workspaceFolder}/src/index.ts",
  "env": {
    "NODE_ENV": "development",
    "DB_HOST": "blindedby.love",
    "DB_PORT": "3306",
    "DB_USER": "mailadmin",
    "DB_PASSWORD": "HOUsc@0202",
    "DB_NAME": "mailserver"
  }
}
```

## 🛠️ 调试技巧

### 1. 查看环境变量加载日志

应用启动时会显示：
```
✅ Environment variables loaded from: /path/to/.env
Environment variables status:
DB_HOST: blindedby.love
DB_PASSWORD: [REDACTED]
NODE_ENV: development
```

### 2. 在代码中添加调试点

在 `src/index.ts` 的环境变量加载部分设置断点：

```typescript
// 在这里设置断点
console.log('DB_HOST:', process.env.DB_HOST);
```

### 3. 使用调试控制台

在调试时，在控制台中输入：
```javascript
process.env.DB_HOST
process.env.DB_PASSWORD
```

## 🔧 常见问题

### 问题1：环境变量为 undefined

**原因**：.env 文件路径错误或格式问题

**解决**：
1. 检查 .env 文件位置
2. 确保文件格式正确（无 BOM，使用 = 分隔）
3. 重启 VS Code

### 问题2：部分环境变量丢失

**原因**：.env 文件中有语法错误

**解决**：
1. 检查 .env 文件中是否有空行或注释问题
2. 确保每行格式为 `KEY=value`
3. 值包含空格时使用引号

### 问题3：调试时环境变量与运行时不同

**原因**：调试配置与运行配置使用不同的环境变量加载方式

**解决**：
1. 使用 `🚀 开发调试 (推荐)` 配置
2. 或在调试配置中添加 `"envFile": "${workspaceFolder}/.env"`

## 📝 最佳实践

1. **使用推荐配置**：优先使用 `🚀 开发调试 (推荐)` 配置
2. **定期测试**：使用 `npm run env:test` 验证环境变量
3. **版本控制**：不要将 .env 文件提交到 git
4. **文档化**：在 .env.example 中记录所需的环境变量

## 🎯 验证成功

当环境变量正确加载时，你会看到：

```
✅ Environment variables loaded from: /path/to/.env
Environment variables status:
DB_HOST: blindedby.love
DB_PASSWORD: [REDACTED]
NODE_ENV: development
Working directory: /path/to/backend
__dirname: /path/to/src
```

数据库连接日志会显示：
```
MySQL connection pool created {
  host: 'blindedby.love',
  port: 3306,
  database: 'mailserver'
}
```

而不是默认的 localhost:3306。
