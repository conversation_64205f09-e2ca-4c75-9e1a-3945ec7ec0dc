import { Router } from 'express';
import {
  register,
  login,
  getCurrentUser,
  refreshToken,
  logout,
} from '../controllers/authController';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// 公开路由
router.post('/register', register);
router.post('/login', login);
router.post('/refresh', refreshToken);

// 需要认证的路由
router.get('/me', authenticateToken, getCurrentUser);
router.post('/logout', authenticateToken, logout);

export default router;
