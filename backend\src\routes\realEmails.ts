/**
 * 真实邮件路由
 * 使用 Postfix + Dovecot + MySQL
 */

import { Router } from 'express';
import {
  getEmails,
  getEmailById,
  sendEmail,
  updateEmail,
  deleteEmail,
  searchEmails,
  getFolders,
  syncEmails,
} from '../controllers/realEmailController';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// 所有邮件路由都需要认证
router.use(authenticateToken);

// 文件夹管理
router.get('/folders', getFolders);

// 邮件CRUD操作
router.get('/', getEmails);
router.get('/:id', getEmailById);
router.post('/', sendEmail);
router.put('/:id', updateEmail);
router.delete('/:id', deleteEmail);

// 邮件搜索
router.post('/search', searchEmails);

// 邮件同步
router.post('/sync', syncEmails);

export default router;
