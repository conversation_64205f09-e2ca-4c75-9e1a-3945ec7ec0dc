# 调试指南

本项目已配置完整的 VS Code 调试环境，支持多种调试场景。

## 🚀 快速开始

### 1. 开发调试（推荐）
- 配置名称：`🚀 开发调试 (推荐)`
- 自动加载 .env 文件
- 直接调试 TypeScript 源码，无需编译
- 支持热重载和断点调试

### 2. 附加到 nodemon 进程
1. 先运行：`npm run dev:debug`
2. 然后选择配置：`🔗 附加到 nodemon`
3. 这样可以在 nodemon 自动重启时保持调试连接

## ⚠️ 环境变量问题解决

如果遇到环境变量读取问题：

1. **使用推荐配置**：选择 `🚀 开发调试 (推荐)` 配置
2. **检查 .env 文件**：确保 `.env` 文件在项目根目录
3. **查看调试输出**：启动时会显示环境变量加载状态

## 📋 所有调试配置

### 开发环境调试
- **🚀 开发调试 (TypeScript)** - 直接调试 TS 源码（推荐）
- **🔧 开发调试 (ts-node)** - 使用 ts-node 调试
- **🔗 附加到 nodemon** - 附加到运行中的 nodemon 进程

### 生产环境调试
- **📦 生产调试 (编译后)** - 调试编译后的 JS 代码

### 测试调试
- **🧪 调试当前测试文件** - 调试当前打开的测试文件
- **🧪 调试所有测试** - 调试整个测试套件

## 🛠️ 使用方法

### 方法一：VS Code 调试面板
1. 按 `Ctrl+Shift+D` 打开调试面板
2. 选择想要的调试配置
3. 点击绿色播放按钮开始调试

### 方法二：快捷键
1. 按 `F5` 启动调试（使用默认配置）
2. 按 `Ctrl+F5` 启动但不调试

### 方法三：命令面板
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Debug: Start Debugging"
3. 选择调试配置

## 🔧 调试技巧

### 设置断点
- 在代码行号左侧点击设置断点
- 右键断点可设置条件断点
- 支持日志断点（不暂停执行，只输出日志）

### 调试控制台
- 在调试时可以在控制台执行表达式
- 查看变量值：直接输入变量名
- 执行代码：输入任意 JavaScript/TypeScript 代码

### 变量监视
- 在调试面板的"监视"区域添加表达式
- 实时查看变量值变化

### 调用堆栈
- 查看函数调用链
- 点击堆栈帧可跳转到对应代码位置

## 📝 npm 脚本

```bash
# 普通开发模式
npm run dev

# 调试模式（支持附加调试器）
npm run dev:debug

# 测试调试
npm run test:debug

# 构建项目
npm run build

# 运行生产版本
npm start
```

## 🐛 常见问题

### 1. 调试器无法连接
- 确保没有其他进程占用调试端口（9229）
- 重启 VS Code 和 Node.js 进程

### 2. 断点不生效
- 确保源码映射（source maps）已启用
- 检查 TypeScript 编译配置
- 尝试重新启动调试会话

### 3. 环境变量问题
- 确保 `.env` 文件存在且配置正确
- 检查环境变量是否正确加载

### 4. 端口冲突
- 主应用端口：3001
- 调试端口：9229
- 测试调试端口：9230

## 🔍 调试最佳实践

1. **使用 TypeScript 调试配置**：直接调试源码，无需编译
2. **设置条件断点**：只在特定条件下暂停
3. **使用日志断点**：不影响程序执行的情况下输出信息
4. **监视关键变量**：实时观察重要数据变化
5. **利用调用堆栈**：理解代码执行流程

## 📚 相关文件

- `.vscode/launch.json` - 调试配置
- `.vscode/tasks.json` - 任务配置
- `.vscode/settings.json` - VS Code 设置
- `nodemon.json` - nodemon 配置
- `tsconfig.json` - TypeScript 配置

## 🎯 下一步

1. 尝试不同的调试配置
2. 在关键代码位置设置断点
3. 使用调试控制台探索代码行为
4. 配置个人偏好的调试设置
