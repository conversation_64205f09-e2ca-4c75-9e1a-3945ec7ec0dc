#!/usr/bin/env node

// 测试环境变量加载脚本
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

console.log('🔍 测试环境变量加载...\n');

// 显示当前工作目录
console.log('当前工作目录:', process.cwd());
console.log('脚本所在目录:', __dirname);

// 尝试多个可能的 .env 文件路径
const envPaths = [
  path.join(process.cwd(), '.env'),           // 项目根目录
  path.join(__dirname, '../.env'),            // 相对于 scripts 目录
  path.join(__dirname, '../../.env'),         // 上级目录
];

console.log('\n📁 检查 .env 文件路径:');
envPaths.forEach((envPath, index) => {
  const exists = fs.existsSync(envPath);
  console.log(`${index + 1}. ${envPath} - ${exists ? '✅ 存在' : '❌ 不存在'}`);
});

console.log('\n🔄 尝试加载环境变量:');
let envLoaded = false;
let loadedPath = '';

for (const envPath of envPaths) {
  try {
    if (fs.existsSync(envPath)) {
      const result = dotenv.config({ path: envPath });
      if (!result.error) {
        console.log(`✅ 成功从 ${envPath} 加载环境变量`);
        envLoaded = true;
        loadedPath = envPath;
        break;
      } else {
        console.log(`❌ 从 ${envPath} 加载失败:`, result.error.message);
      }
    }
  } catch (error) {
    console.log(`❌ 从 ${envPath} 加载异常:`, error.message);
  }
}

if (!envLoaded) {
  console.log('⚠️  未能加载任何 .env 文件');
}

console.log('\n📊 环境变量状态:');
const envVars = [
  'NODE_ENV',
  'PORT',
  'DB_HOST',
  'DB_PORT',
  'DB_USER',
  'DB_PASSWORD',
  'DB_NAME',
  'JWT_SECRET',
  'FRONTEND_URL'
];

envVars.forEach(varName => {
  const value = process.env[varName];
  if (varName === 'DB_PASSWORD' || varName === 'JWT_SECRET') {
    console.log(`${varName}: ${value ? '[已设置]' : '❌ 未设置'}`);
  } else {
    console.log(`${varName}: ${value || '❌ 未设置'}`);
  }
});

console.log('\n🎯 建议:');
if (envLoaded) {
  console.log('✅ 环境变量加载正常');
  console.log(`📍 使用的配置文件: ${loadedPath}`);
} else {
  console.log('❌ 环境变量加载失败');
  console.log('请检查:');
  console.log('1. .env 文件是否存在于项目根目录');
  console.log('2. .env 文件格式是否正确');
  console.log('3. 文件权限是否正确');
}
