{
  // TypeScript 配置
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  
  // 调试配置
  "debug.allowBreakpointsEverywhere": true,
  "debug.inlineValues": "auto",
  "debug.showBreakpointsInOverviewRuler": true,
  "debug.terminal.clearBeforeReusing": true,
  
  // Node.js 调试
  "debug.node.autoAttach": "smart",
  "debug.javascript.autoAttachFilter": "smart",
  
  // 文件监视
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/logs/**": true,
    "**/uploads/**": true,
    "**/.git/**": true
  },
  
  // 搜索排除
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/logs": true,
    "**/uploads": true,
    "**/.git": true,
    "**/coverage": true
  },
  
  // 文件关联
  "files.associations": {
    "*.env*": "dotenv"
  },
  
  // 终端配置
  "terminal.integrated.env.windows": {
    "NODE_ENV": "development"
  },
  
  // Jest 配置
  "jest.jestCommandLine": "npm test --",
  "jest.autoRun": "off",
  
  // 编辑器配置
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  
  // 排除文件
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true
  }
}
