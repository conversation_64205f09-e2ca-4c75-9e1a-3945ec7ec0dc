import request from 'supertest';
import app from '../src/app';
import { TestHelper } from './helpers/testUtils';

describe('Email API', () => {
  let testUser: any;
  let authToken: string;

  beforeEach(async () => {
    await TestHelper.cleanupTestData();
    const { user, token } = await TestHelper.createTestUser();
    testUser = user;
    authToken = token;
  });

  describe('GET /api/emails', () => {
    it('should get emails list with authentication', async () => {
      // 创建测试邮件
      await TestHelper.createTestEmail(testUser.id);
      await TestHelper.createTestEmail(testUser.id, { subject: '第二封邮件' });

      const response = await request(app)
        .get('/api/emails')
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('total');
      expect(response.body.data).toHaveProperty('page');
      expect(response.body.data).toHaveProperty('limit');
      expect(Array.isArray(response.body.data.data)).toBe(true);
      expect(response.body.data.data.length).toBeGreaterThan(0);
    });

    it('should support pagination', async () => {
      // 创建多封测试邮件
      for (let i = 0; i < 5; i++) {
        await TestHelper.createTestEmail(testUser.id, { subject: `邮件 ${i + 1}` });
      }

      const response = await request(app)
        .get('/api/emails?page=1&limit=3')
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.data.length).toBeLessThanOrEqual(3);
      expect(response.body.data.page).toBe(1);
      expect(response.body.data.limit).toBe(3);
    });

    it('should filter by folder', async () => {
      await TestHelper.createTestEmail(testUser.id, { folderId: `inbox-${testUser.id}` });
      await TestHelper.createTestEmail(testUser.id, { folderId: `sent-${testUser.id}` });

      const response = await request(app)
        .get(`/api/emails?folderId=inbox-${testUser.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateApiResponse(response, 200);
      response.body.data.data.forEach((email: any) => {
        expect(email.folderId).toBe(`inbox-${testUser.id}`);
      });
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/api/emails');

      TestHelper.validateErrorResponse(response, 401);
    });
  });

  describe('GET /api/emails/:id', () => {
    it('should get email by id', async () => {
      const email = await TestHelper.createTestEmail(testUser.id);

      const response = await request(app)
        .get(`/api/emails/${email.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.id).toBe(email.id);
      expect(response.body.data.subject).toBe(email.subject);
    });

    it('should fail for non-existent email', async () => {
      const response = await request(app)
        .get('/api/emails/non-existent-id')
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateErrorResponse(response, 404);
    });

    it('should fail for email belonging to another user', async () => {
      const { user: otherUser } = await TestHelper.createTestUser();
      const email = await TestHelper.createTestEmail(otherUser.id);

      const response = await request(app)
        .get(`/api/emails/${email.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateErrorResponse(response, 403);
    });
  });

  describe('POST /api/emails', () => {
    it('should send email successfully', async () => {
      const emailData = {
        to: ['<EMAIL>'],
        subject: '测试邮件主题',
        content: '这是邮件内容',
      };

      const response = await request(app)
        .post('/api/emails')
        .set(TestHelper.getAuthHeader(authToken))
        .send(emailData);

      TestHelper.validateApiResponse(response, 201);
      expect(response.body.data.subject).toBe(emailData.subject);
      expect(response.body.data.recipients).toEqual(emailData.to);
      expect(response.body.data.senderEmail).toBe(testUser.email);
    });

    it('should send email with cc and bcc', async () => {
      const emailData = {
        to: ['<EMAIL>'],
        cc: ['<EMAIL>'],
        bcc: ['<EMAIL>'],
        subject: '测试邮件主题',
        content: '这是邮件内容',
      };

      const response = await request(app)
        .post('/api/emails')
        .set(TestHelper.getAuthHeader(authToken))
        .send(emailData);

      TestHelper.validateApiResponse(response, 201);
      expect(response.body.data.ccRecipients).toEqual(emailData.cc);
      expect(response.body.data.bccRecipients).toEqual(emailData.bcc);
    });

    it('should fail without recipients', async () => {
      const emailData = {
        subject: '测试邮件主题',
        content: '这是邮件内容',
      };

      const response = await request(app)
        .post('/api/emails')
        .set(TestHelper.getAuthHeader(authToken))
        .send(emailData);

      TestHelper.validateErrorResponse(response, 400);
    });

    it('should fail without subject', async () => {
      const emailData = {
        to: ['<EMAIL>'],
        content: '这是邮件内容',
      };

      const response = await request(app)
        .post('/api/emails')
        .set(TestHelper.getAuthHeader(authToken))
        .send(emailData);

      TestHelper.validateErrorResponse(response, 400);
    });

    it('should fail without content', async () => {
      const emailData = {
        to: ['<EMAIL>'],
        subject: '测试邮件主题',
      };

      const response = await request(app)
        .post('/api/emails')
        .set(TestHelper.getAuthHeader(authToken))
        .send(emailData);

      TestHelper.validateErrorResponse(response, 400);
    });
  });

  describe('PUT /api/emails/:id', () => {
    it('should update email status', async () => {
      const email = await TestHelper.createTestEmail(testUser.id, { isRead: false });

      const response = await request(app)
        .put(`/api/emails/${email.id}`)
        .set(TestHelper.getAuthHeader(authToken))
        .send({ isRead: true });

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.isRead).toBe(true);
    });

    it('should update email star status', async () => {
      const email = await TestHelper.createTestEmail(testUser.id, { isStarred: false });

      const response = await request(app)
        .put(`/api/emails/${email.id}`)
        .set(TestHelper.getAuthHeader(authToken))
        .send({ isStarred: true });

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.isStarred).toBe(true);
    });

    it('should move email to different folder', async () => {
      const email = await TestHelper.createTestEmail(testUser.id, { folderId: `inbox-${testUser.id}` });

      const response = await request(app)
        .put(`/api/emails/${email.id}`)
        .set(TestHelper.getAuthHeader(authToken))
        .send({ folderId: `trash-${testUser.id}` });

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data.folderId).toBe(`trash-${testUser.id}`);
    });

    it('should fail for non-existent email', async () => {
      const response = await request(app)
        .put('/api/emails/non-existent-id')
        .set(TestHelper.getAuthHeader(authToken))
        .send({ isRead: true });

      TestHelper.validateErrorResponse(response, 404);
    });
  });

  describe('DELETE /api/emails/:id', () => {
    it('should delete email successfully', async () => {
      const email = await TestHelper.createTestEmail(testUser.id);

      const response = await request(app)
        .delete(`/api/emails/${email.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateApiResponse(response, 200);
    });

    it('should fail for non-existent email', async () => {
      const response = await request(app)
        .delete('/api/emails/non-existent-id')
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateErrorResponse(response, 404);
    });

    it('should fail for email belonging to another user', async () => {
      const { user: otherUser } = await TestHelper.createTestUser();
      const email = await TestHelper.createTestEmail(otherUser.id);

      const response = await request(app)
        .delete(`/api/emails/${email.id}`)
        .set(TestHelper.getAuthHeader(authToken));

      TestHelper.validateErrorResponse(response, 403);
    });
  });
});
