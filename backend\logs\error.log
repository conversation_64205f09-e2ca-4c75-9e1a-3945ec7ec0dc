{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 01:54:37"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 01:58:22"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 01:59:13"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 01:59:13"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 01:59:13"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 01:59:20"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 02:00:20"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 02:00:46"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 02:00:46"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 02:00:55"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 02:01:05"}
{"code":"ECONNREFUSED","level":"error","message":"Database connection test failed with details:","name":"AggregateError","service":"email-system-backend","stack":"AggregateError\n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)","timestamp":"2025-06-15 02:01:19"}
{"body":{"email":"<EMAIL>","password":"123456"},"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"trbdc0t4toj3y7p0idi0xt","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-15 02:03:31","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"email":"<EMAIL>","password":"123456"},"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"wqkckmky9xqx0tdqzr6q5","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:115:11","timestamp":"2025-06-15 02:03:38","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"level":"error","message":"Failed to create user: Field 'domain_id' doesn't have a default value","service":"email-system-backend","sql":"\n      INSERT INTO users (id, email, username, display_name, avatar_url, password_hash, is_active, created_at, updated_at)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ","sqlMessage":"Field 'domain_id' doesn't have a default value","sqlState":"HY000","stack":"Error: Field 'domain_id' doesn't have a default value\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at MySQLDatabase.createUser (C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\database\\mysqlDatabase.ts:22:23)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:46:33","timestamp":"2025-06-15 02:03:56"}
{"body":{"displayName":"iam","email":"<EMAIL>","password":"123@qwe","username":"iam"},"ip":"::1","level":"error","message":"Error occurred: Field 'domain_id' doesn't have a default value","method":"POST","params":{},"query":{},"requestId":"cwdqvbq9nyealho8c4dnwk","service":"email-system-backend","stack":"Error: Field 'domain_id' doesn't have a default value\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at MySQLDatabase.createUser (C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\database\\mysqlDatabase.ts:22:23)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:46:33","timestamp":"2025-06-15 02:03:56","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"displayName":"�����û�2","email":"<EMAIL>","password":"123456","username":"testuser2"},"ip":"::1","level":"error","message":"Error occurred: 域名 nonexistent.com 不存在或未激活","method":"POST","params":{},"query":{},"requestId":"95xmnyd1m9fo5ttj2nmblg","service":"email-system-backend","stack":"Error: 域名 nonexistent.com 不存在或未激活\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:49:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-15 02:13:18","url":"/api/auth/register","userAgent":"curl/8.6.0"}
{"body":{"displayName":"iam","email":"<EMAIL>","password":"123@qwe","username":"iam"},"ip":"::1","level":"error","message":"Error occurred: 域名 blinededby.love 不存在或未激活","method":"POST","params":{},"query":{},"requestId":"sbf0c6p946plup34rkgnj","service":"email-system-backend","stack":"Error: 域名 blinededby.love 不存在或未激活\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:49:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-15 02:15:24","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"email":"<EMAIL>","password":"123456"},"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"h17hz72ijwae2a65hk9kmk","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:151:11","timestamp":"2025-06-15 02:17:25","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"displayName":"iam","email":"<EMAIL>","password":"123@qwe","username":"iam"},"ip":"::1","level":"error","message":"Error occurred: 域名 blinededby.love 不存在或未激活","method":"POST","params":{},"query":{},"requestId":"eaosfg53k3mdunp6hxbwj","service":"email-system-backend","stack":"Error: 域名 blinededby.love 不存在或未激活\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:49:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-15 02:17:53","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","params":{},"query":{},"requestId":"ahu55mb20fqypomyoc9q","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:186:3)","timestamp":"2025-06-15 02:19:04","url":"/api/auth/logout","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"email":"<EMAIL>","password":"123@qwe"},"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"y2jr34goc6scn4mhmxsq6w","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:140:11\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-15 02:19:11","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"email":"<EMAIL>","password":"123456"},"ip":"::1","level":"error","message":"Error occurred: 邮箱或密码错误","method":"POST","params":{},"query":{},"requestId":"h9307vgla0ttxl0q801d","service":"email-system-backend","stack":"Error: 邮箱或密码错误\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\authController.ts:151:11","timestamp":"2025-06-15 02:19:54","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: 访问令牌缺失","method":"POST","params":{},"query":{},"requestId":"wlqxk3tpzmdscx06k5625","service":"email-system-backend","stack":"Error: 访问令牌缺失\n    at authenticateToken (C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\auth.ts:18:13)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:186:3)","timestamp":"2025-06-15 02:20:28","url":"/api/auth/logout","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"1mludmb2e78ybccnqcqcf","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:21:37","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"oceaiogli9jp89gtolvs","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:21:38","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"5hsdpu10liendx42gq5vg","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:21:40","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"z3wfrvuhmmh0ons49dna8x","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:21:44","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"de4x2v5wl3hx74c32e8wv","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:22:13","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"au01vho7x6bsu5l4t41je","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:22:14","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"pto0upycgvlqfucszl5rkj","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:22:16","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"2kv8u9kvfj5gf5l75wid","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:05","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"4y69p2uofsdx5itjwyjg1o","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:06","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"myjzhecdp8bmscknyz008","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:09","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"sr0k5qzuq6fwc616y9f4td","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:13","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"029uct99ggjylaj3qz9tbsr","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:51","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"211lw37c8sqr87no9h3x4","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:52","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"lpc5kj4brbcm1ilgm4q2z","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:23:54","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"r5z5hpogs7dogf4x8t67rg","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:03","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"r8wg59w8sm812bbw1ar3bl","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:04","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"90tkwmd2tlka8bd8m5qj","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:05","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"wiamhvtzx0ha3rhymvkcuc","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:07","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"bt05fhzg9kgz1bihpwarl","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:11","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"de3moxn9mnfkacqtndvdd","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:27","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"5kzglp9oiaqx7ek32zc73","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:28","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"155fi5ipd3zhbaqq35pr1uk","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:30","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"4w393bdnn5y8z21hc11wgf","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:27:34","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"62mthyfjitrww6xjwk4z3","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:29:17","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"ttptwfywjg9hyp1y1hpd0a","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:29:18","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"oa6wmwyhm9qv9n1zo9u58r","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:29:20","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"iyjy3kuxjfrmutnxuz0zkf","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:29:24","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"xaob5acu0xie24yixb37a","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:31:14","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"8gbbaehd6nar6s4t8su2ds","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:31:15","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"t6zfajkvf2i5u3xyc347oa","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:31:18","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot destructure property 'to' of 'req.body' as it is undefined.","method":"POST","params":{},"query":{},"requestId":"mrgwwcb3shut3v2u14cfk","service":"email-system-backend","stack":"TypeError: Cannot destructure property 'to' of 'req.body' as it is undefined.\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\controllers\\realEmailController.ts:107:5\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\backend\\src\\middleware\\errorHandler.ts:108:21\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:157:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\route.js:117:3)\n    at handle (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:435:11)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\lib\\layer.js:152:17)\n    at C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:295:15\n    at processParams (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\emailSystem\\node_modules\\router\\index.js:291:5)","timestamp":"2025-06-15 02:31:22","url":"/api/emails","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"code":"ECONNREFUSED","fatal":true,"level":"error","message":"Database connection test failed:","service":"email-system-backend","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)\n    at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17)","timestamp":"2025-06-15 02:50:07"}
{"code":"ECONNREFUSED","fatal":true,"level":"error","message":"Database connection test failed:","service":"email-system-backend","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1116:18)\n    at afterConnectMultiple (node:net:1683:7)\n    at TCPConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17)","timestamp":"2025-06-15 02:51:13"}
