import { Request, Response, NextFunction } from 'express';
import { AppError, ErrorType, ErrorCode } from '../types';
import { logger } from '../utils/logger';

// 错误响应接口
interface ErrorResponse {
  success: false;
  error: string;
  errorType?: ErrorType;
  errorCode?: ErrorCode;
  details?: any;
  timestamp: string;
  requestId?: string;
  stack?: string;
}

// 生成请求ID
const generateRequestId = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// 全局错误处理中间件
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let err = error;
  const requestId = generateRequestId();

  // 记录详细错误日志
  logger.error('Error occurred:', {
    requestId,
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
    query: req.query,
    params: req.params,
  });

  // 如果不是AppError，转换为AppError
  if (!(err instanceof AppError)) {
    // 处理常见的系统错误
    if (err.name === 'ValidationError') {
      err = new AppError('输入数据验证失败', 400, ErrorType.VALIDATION, ErrorCode.INVALID_INPUT);
    } else if (err.name === 'CastError') {
      err = new AppError('无效的数据格式', 400, ErrorType.VALIDATION, ErrorCode.INVALID_INPUT);
    } else if (err.name === 'MongoError' || err.name === 'MongooseError') {
      err = new AppError('数据库操作失败', 500, ErrorType.DATABASE_ERROR, ErrorCode.DATABASE_CONNECTION_ERROR);
    } else if (err.message.includes('ECONNREFUSED') || err.message.includes('ENOTFOUND')) {
      err = new AppError('网络连接失败', 500, ErrorType.NETWORK_ERROR, ErrorCode.EXTERNAL_SERVICE_ERROR);
    } else {
      err = new AppError('服务器内部错误', 500, ErrorType.SERVER_ERROR, ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  const appError = err as AppError;

  // 构建错误响应
  const errorResponse: ErrorResponse = {
    success: false,
    error: appError.message,
    errorType: appError.errorType,
    errorCode: appError.errorCode,
    timestamp: new Date().toISOString(),
    requestId,
  };

  // 开发环境返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = appError.stack;
    errorResponse.details = appError.details || (error.message !== appError.message ? error.message : undefined);
  } else {
    // 生产环境只返回安全的错误信息
    if (!appError.isOperational) {
      errorResponse.error = '服务器内部错误';
      errorResponse.errorType = ErrorType.SERVER_ERROR;
      errorResponse.errorCode = ErrorCode.INTERNAL_SERVER_ERROR;
      delete errorResponse.details;
    }
  }

  // 设置响应头
  res.set({
    'X-Request-ID': requestId,
    'Content-Type': 'application/json',
  });

  res.status(appError.statusCode).json(errorResponse);
};

// 404错误处理
export const notFoundHandler = (req: Request, res: Response): void => {
  res.status(404).json({
    success: false,
    error: `路由 ${req.originalUrl} 不存在`,
  });
};

// 异步错误捕获包装器
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
