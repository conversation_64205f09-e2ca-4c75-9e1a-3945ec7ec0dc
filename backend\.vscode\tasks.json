{"version": "2.0.0", "tasks": [{"label": "npm: build", "type": "npm", "script": "build", "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "npm: dev", "type": "npm", "script": "dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "typescript", "source": "ts", "applyTo": "closedDocuments", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "(?:.*?Local:\\s+(https?://.*?).*|.*?ready in \\d+ms)"}}}}, {"label": "npm: test", "type": "npm", "script": "test", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "npm: test:watch", "type": "npm", "script": "test:watch", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true}]}